//
//  ServiceScreen.swift
//  Appio
//
//  Created by gondo on 06/03/2025.
//

import SwiftUI

struct ServiceScreen: View {
    @Environment(\.horizontalSizeClass) var horizontalSizeClass
    @Environment(\.colorScheme) var colorScheme

    @StateObject private var viewModel: ServiceScreenViewModel

    @State private var isPreviewSheetOpened: Bool = false // has to use this. if using viewModel.service.showPreview directly, the sheet height settings is not respected and it openes sheet on the full screen
    @State private var isPushNotificationPermissionsOpened: Bool = false
    @State private var isServiceInfoSheetOpened: Bool = false

    init(_ service: ServiceEntity) {
        _viewModel = StateObject(wrappedValue: ServiceScreenViewModel(service: service))
    }

    var body: some View {
        NotificationsList(viewModel: viewModel)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        isServiceInfoSheetOpened = true
                    } label: {
                        Image(systemName: "info.circle")
                            .foregroundStyle(ThemeManager.accent(for: viewModel.service, colorScheme: colorScheme))
                    }
                }
            }
            .overlay {
                if !isPreviewSheetOpened {
                    RequestPushNotificationPermissions(
                        isOpen: $isPushNotificationPermissionsOpened,
                        serviceTitle: viewModel.service.title
                    )
                } else {
                    BackgroundColor() // hide content like "No notifications"
                }
            }
            .sheet(isPresented: $isPreviewSheetOpened) {
                ZStack {
                    ServicePreview(
                        service: viewModel.service,
                        onDone: viewModel.markPreviewAsViewed
                    )
                }
                .presentationDragIndicator(.hidden)
                .presentationDetents(
                    horizontalSizeClass == .compact ? [.medium] : [.large]
                ) // .presentationDetents([.height(360)])
                .presentationCornerRadius(UIConstants.sheetCornerRadius)
                .interactiveDismissDisabled()
            }
            .sheet(isPresented: $isServiceInfoSheetOpened) {
                ServiceDetailView(service: viewModel.service, device: viewModel.device)
                    .presentationDetents([.large])
                    .presentationCornerRadius(UIConstants.sheetCornerRadius)
            }
            .onAppear {
                viewModel.reloadService() // required to prevent constant preview opening
                isPushNotificationPermissionsOpened = false

                if viewModel.service.showPreview {
                    // animate opening
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                            isPreviewSheetOpened = true
                        }
                    }
                }
            }
            .onShake {
                isPushNotificationPermissionsOpened = false
            }
    }
}

#if DEBUG
#Preview() {
    NavigationStack {
        ServiceScreen(ServiceEntity.mockB)
    }
}

#Preview("With Preview") {
    NavigationStack {
        ServiceScreen(ServiceEntity.mock)
    }
}

#Preview("Colored") {
    NavigationStack {
        ServiceScreen(ServiceEntity.mockColored)
    }
}
#endif
