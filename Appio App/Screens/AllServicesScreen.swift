//
//  AllServicesScreen.swift
//  Appio
//
//  Created by gondo on 06/03/2025.
//

import SwiftUI

struct AllServicesScreen: View {
    let services: [ServiceEntity]

    var body: some View {
        List(services, id: \.id) { service in
            NavigationLink(value: service) {
                VStack(spacing: 0) {
                    AsyncImageView(urlString: service.bannerURL)
                        .aspectRatio(16 / 9, contentMode: .fit)
                        .clipped()
                        .accessibilityLabel(Text("\(service.title) Banner Image"))
                        .clipShape(ContainerRelativeShape())
                        .overlay(
                            ContainerRelativeShape()
                                .strokeBorder(.ultraThinMaterial)
                        )
 
                    HStack(alignment: .top, spacing: 0) {
                        VStack(spacing: 0) {
                            AsyncImageView(
                                urlString: service.logoURL,
                                cornerRadius: UIConstants.logoCornerRadius
                            )
                            .frame(width: UIConstants.logoSize, height: UIConstants.logoSize)
                        }
                        .padding(.trailing, 16)

                        VStack(alignment: .leading) {
                            Text(service.title)
                                .font(.title2)
                                .fontWeight(.semibold)
                                .lineLimit(1)
                            Text(service.description ?? "")
                                .font(.callout)
                                .lineLimit(2)
                        }

                        Spacer(minLength: 0)
                    }
                    .padding(16)
                }
                .background(Color(UIColor.tertiarySystemGroupedBackground))
                .cornerRadius(UIConstants.bigCellCornerRadius)
                .overlay(
                    RoundedRectangle(cornerRadius: UIConstants.bigCellCornerRadius)
                        .strokeBorder(Color(UIColor.systemBackground).opacity(0.3))
                )
                .shadow(color: .black.opacity(0.15), radius: 8, x: 0, y: 4)
            }
            .padding(.top, 32)
            .buttonStyle(.plain)
            .listRowInsets(EdgeInsets(top: 0, leading: 24, bottom: 0, trailing: 8))
            .listRowBackground(Color.clear)
            .listRowSeparator(.hidden)
        }
        .listStyle(.plain)
    }
}

#if DEBUG
    #Preview {
        ZStack {
            BackgroundColor()
            AllServicesScreen(services: [ServiceEntity.mock, ServiceEntity.mockB])
        }
    }
#endif
