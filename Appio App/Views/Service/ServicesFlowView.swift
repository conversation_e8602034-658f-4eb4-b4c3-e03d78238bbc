//
//  ServicesFlowView.swift
//  Appio App
//
//

import SwiftUI

struct ServicesFlowView: View {
    @EnvironmentObject var appViewModel: AppViewModel

    var body: some View {
        NavigationStack(path: $appViewModel.navigationPath) {
            if appViewModel.services.count > 1 {
                AllServicesScreen(services: appViewModel.services)
                    .id("all-services")
                    .accessibilityLabel("All services screen")
                    .navigationDestination(for: ServiceEntity.self) { service in
                        ServiceScreen(service)
                            .id(service.id) // Force refresh when service changes
                    }
                    .navigationDestination(for: NotificationEntity.self) { notification in
                        if let service = ServiceManager.get(id: notification.serviceId) {
                            NotificationDetails(notification: notification, service: service)
                        } else {
                            NotificationDetails(notification: notification, service: nil)
                        }
                    }
            } else if let service = appViewModel.currentService ?? appViewModel.services.first {
                ServiceScreen(service)
                    .id("service-\(service.id)")
                    .accessibilityLabel("\(service.title) screen")
                    .navigationDestination(for: NotificationEntity.self) { notification in
                        if let service = ServiceManager.get(id: notification.serviceId) {
                            NotificationDetails(notification: notification, service: service)
                        } else {
                            NotificationDetails(notification: notification, service: nil)
                        }
                    }
            }
        }
        .id("nav-services-\(appViewModel.services.map(\.id).joined())-\(appViewModel.currentService?.id ?? "none")") // required to regenerate list when new service is added
    }
}

#if DEBUG
#Preview("Multiple Services") {
    ServicesFlowView()
        .environmentObject(AppViewModel())
}

#Preview("Single Service") {
    let viewModel = AppViewModel()
    viewModel.navigateToService(serviceId: ServiceEntity.mock.id)
    return ServicesFlowView()
        .environmentObject(viewModel)
}

#Preview("No Services") {
    ServicesFlowView()
        .environmentObject(AppViewModel())
}
#endif
