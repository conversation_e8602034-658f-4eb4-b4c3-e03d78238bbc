//
//  ServicePreview.swift
//  Appio
//
//  Created by gondo on 10/03/2025.
//

import SwiftUI

struct ServicePreview: View {
    @Environment(\.dismiss) var dismiss
    @Environment(\.colorScheme) var colorScheme

    let service: ServiceEntity
    let onDone: () -> Void

    var body: some View {
        ZStack {
            // Apply service background color
            ThemeManager.primaryBackground(for: service, colorScheme: colorScheme)
                .ignoresSafeArea(.all)

            VStack(spacing: 0) {
                AsyncImageView(urlString: service.bannerURL)
                    .aspectRatio(16 / 9, contentMode: .fit)
                    .clipped()
                    .accessibilityLabel(Text("\(service.title) Banner Image"))
                    .clipShape(ContainerRelativeShape())
                    .overlay(
                        ContainerRelativeShape()
                            .strokeBorder(.ultraThinMaterial)
                    )

                VStack {
                    HStack(alignment: .top, spacing: 24) {
                        AsyncImageView(
                            urlString: service.logoURL,
                            cornerRadius: UIConstants.logoCornerRadius
                        )
                        .frame(width: UIConstants.logoSize, height: UIConstants.logoSize)
                        .accessibilityLabel(Text("\(service.title) Logo"))

                        VStack(alignment: .leading) {
                            Text(service.title)
                                .font(.title2)
                                .fontWeight(.semibold)
                                .foregroundStyle(ThemeManager.primaryText(for: service, colorScheme: colorScheme))
                                .accessibilityAddTraits(.isHeader)
                            Text(service.description ?? "")
                                .font(.callout)
                                .foregroundStyle(ThemeManager.secondaryText(for: service, colorScheme: colorScheme))
                        }

                        Spacer()
                    }

                    Spacer()

                    AppioButton("Done") {
                        onDone()
                        dismiss()
                    }
                    .tint(ThemeManager.accent(for: service, colorScheme: colorScheme))
                    .padding(.vertical, UIConstants.largeSpacing)
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)

                Spacer()
            }
        }
        .ignoresSafeArea(.all)
    }
}

#if DEBUG
#Preview {
    ServicePreview(
        service: ServiceEntity.mock,
        onDone: { print("Done tapped in preview") }
    )
}

#Preview("Colored") {
    ServicePreview(
        service: ServiceEntity.mockColored,
        onDone: { print("Done tapped in preview") }
    )
}
#endif
