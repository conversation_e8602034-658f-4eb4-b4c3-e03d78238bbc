//
//  PoweredByView.swift
//  Appio
//
//  Created by gondo on 09/09/2025.
//

import SwiftUI

struct PoweredByView: View {
    let service: ServiceEntity?

    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        Button {
            print("clicked Powered by Appio")
            if let url = URL(string: "https://appio.so/?source=ios&service=\(service?.id ?? "")") {
                UIApplication.shared.open(url, options: [:], completionHandler: nil)
            }
        } label: {
            Text("Powered by Appio")
                .font(.footnote)
                .foregroundStyle(ThemeManager.secondaryText(for: service, colorScheme: colorScheme))
        }
        .buttonStyle(.plain)
    }
}

#Preview {
    PoweredByView(service: ServiceEntity.mock)
}
