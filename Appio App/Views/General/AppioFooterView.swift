//
//  AppioFooterView.swift
//  Appio
//
//  Created by gondo on 09/09/2025.
//

import SwiftUI

struct AppioFooterView: View {
    let service: ServiceEntity?
    let device: DeviceEntity?

    @State private var isDebugOpened: Bool = false
    @State private var showFeedback: Bool = false
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack {
            Button {
                showFeedback = true
            } label: {
                Text("Send Feedback")
                    .font(.callout)
                    .foregroundStyle(ThemeManager.primaryText(for: service, colorScheme: colorScheme))
            }
            .padding(.bottom, 8)
            
            PoweredByView(service: service)
            
            Text("Version: \(AppVersion.current())")
                .font(.footnote)
                .foregroundStyle(ThemeManager.secondaryText(for: service, colorScheme: colorScheme))
                .onTapGesture(count: 3) {
                    isDebugOpened.toggle()
                }
            
            if isDebugOpened, device != nil || service != nil {
                VStack {
                    if let device = device {
                        Text("\(device.id)")
                            .onTapGesture {
                                UIPasteboard.general.string = device.id
                            }
                    }
                    
                    if let service = service {
                        Text("\(service.id)")
                            .onTapGesture {
                                UIPasteboard.general.string = service.id
                            }
                        Text("\(service.customerUserId)")
                            .onTapGesture {
                                UIPasteboard.general.string = service.customerUserId
                            }
                    }
                }
                .font(.footnote)
                .foregroundStyle(ThemeManager.secondaryText(for: service, colorScheme: colorScheme))
                .padding(.top, 8)
            }
        }
        .sheet(isPresented: $showFeedback) {
            FeedbackView(service: service, device: device, onDismiss: {
                showFeedback = false
            })
            .presentationDragIndicator(.visible)
        }
    }
}

#Preview("Nils") {
    #if DEBUG
    AppioFooterView(service: nil, device: nil)
    #endif
}

#Preview("Values") {
    #if DEBUG
    AppioFooterView(service: ServiceEntity.mock, device: DeviceEntity.mock)
    #endif
}

