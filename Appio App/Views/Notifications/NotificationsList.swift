//
//  NotificationsList.swift
//  Appio
//
//  Created by gondo on 18/03/2025.
//

import SwiftUI

struct NotificationsList: View {
    @Environment(\.scenePhase) private var scenePhase
    @Environment(\.colorScheme) var colorScheme
    @ObservedObject var viewModel: ServiceScreenViewModel
    @State private var searchText = ""
    @State private var showFilters = false
    @State private var filterOptions = FilterOptions()
    @State private var isSearching = false
    @State private var isWidgetTutorialVisible = false

    var filteredNotifications: [NotificationEntity] {
        var notifications = viewModel.notifications

        if filterOptions.linksOnly {
            notifications = notifications.filter { $0.payload.link?.isEmpty == false }
        }

        if filterOptions.imagesOnly {
            notifications = notifications.filter { $0.payload.imageUrl?.isEmpty == false }
        }

        notifications = notifications.filter { filterOptions.dateRange.isDateIncluded($0.receivedAt) }

        if !searchText.isEmpty {
            notifications = notifications.filter {
                $0.payload.subtitle.localizedCaseInsensitiveContains(searchText) ||
                    $0.payload.message.localizedCaseInsensitiveContains(searchText)
            }
        }

        return notifications
    }
    
    
    @State private var noDataAnimationTrigger = false
    
    var noDataView: some View {
        ZStack {
            if WidgetsManager.showTutorialBanner(service: viewModel.service) {
                VStack {
                    bannerView
                        .padding(.horizontal, 16)
                        .padding(.bottom, 16)

                    Spacer()
                }
            }
            
            VStack(spacing: 16) {
                Spacer()

//                Text("No Notifications")
//                    .font(.title3)
//                    .foregroundStyle(.secondary)
//                Button("Refresh") {
//                    viewModel.refreshNotifications()
//                }

                if #available(iOS 18.0, *) {
                    Image(systemName: "checkmark.circle")
                        .font(.system(size: 64))
                        .foregroundStyle(.green)
                        .symbolEffect(.bounce, options: .nonRepeating, value: noDataAnimationTrigger)
                        .onTapGesture { noDataAnimationTrigger.toggle() }
                        .onAppear {
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                noDataAnimationTrigger.toggle()
                            }
                        }
                        .onChange(of: scenePhase) { _, newValue in
                            if newValue == .active {
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                    noDataAnimationTrigger.toggle()
                                }
                            }
                        }
                } else {
                    Image(systemName: "checkmark.circle")
                        .font(.system(size: 64))
                        .foregroundStyle(.green)
                }
                
                Text("Setup completed")
                    .foregroundStyle(ThemeManager.primaryText(for: viewModel.service, colorScheme: colorScheme))
                    .padding(.vertical, UIConstants.smallSpacing)
                
                if let link = viewModel.service.URL,
                   let url = URL(string: link),
                   UIApplication.shared.canOpenURL(url)
                {
                    AppioButton("Done     ") {
                        UIApplication.shared.open(url)
                    }
                    .tint(ThemeManager.accent(for: viewModel.service, colorScheme: colorScheme))
                    .fixedSize()
                }
                
                Spacer()
            }
        }
    }

    var noResultView: some View {
        VStack(spacing: 16) {
            Spacer()
            Text("No Result")
                .font(.title3)
                .foregroundStyle(.secondary)
            Spacer()
        }
    }

    var bannerView: some View {
        HStack(spacing: 16) {
            Image(systemName: "widget.small.badge.plus")
                .resizable()
                .scaledToFit()
                .frame(width: 28)

            Text("Try our widget on your homescreen.")

            Spacer()

            Button("Show Me") {
                isWidgetTutorialVisible = true
            }
            .buttonStyle(.borderedProminent)
            .tint(ThemeManager.accent(for: viewModel.service, colorScheme: colorScheme))
            
        }
        .padding(16)
        .background(Color(UIColor.systemIndigo)) // #3ea6ff
        .foregroundStyle(.white)
        .cornerRadius(UIConstants.smallCellCornerRadius)
        .sheet(isPresented: $isWidgetTutorialVisible) {
            WidgetTutorialView()
        }
    }

    var filterButton: some View {
        Button {
            print("Filter")
            showFilters.toggle()
        } label: {
            Image(systemName: "line.3.horizontal.decrease.circle\(filterOptions.isFiltered() ? ".fill" : "")")
                .font(.system(size: 22, weight: .light))
        }
        .padding(.leading, UIConstants.largeSpacing)
        .sheet(isPresented: $showFilters) {
            FilterView(filterOptions: $filterOptions)
                .presentationDetents([.medium])
        }
    }

    @State private var footerViewRefreshAnimate = false
    
    var footerView: some View {
        HStack {
            filterButton

            Spacer()

            Text("\(filteredNotifications.count) \(filteredNotifications.count == 1 ? "notification" : "notifications")")
                .font(.caption)
                .foregroundStyle(ThemeManager.secondaryText(for: viewModel.service, colorScheme: colorScheme))

            Spacer()

            Button {
                viewModel.refreshNotifications()
                footerViewRefreshAnimate.toggle()
            } label: {
                if #available(iOS 18.0, *) {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 22, weight: .light))
                        .symbolEffect(.rotate, options: .speed(5), value: footerViewRefreshAnimate)
                } else {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 22, weight: .light))
                }
            }
            .padding(.trailing, UIConstants.largeSpacing)
        }
        .padding(.vertical, 16)
    }

    var footRowView: some View {
        VStack(spacing: 0) {
            Divider()
            footerView
        }
        .background(Color(.systemBackground))
        .ignoresSafeArea()
    }

    var notificationsList: some View {
        ScrollView {
            LazyVStack(spacing: 0, pinnedViews: [.sectionHeaders]) {
                Section(
                    header: Group {
                        if WidgetsManager.showTutorialBanner(service: viewModel.service) {
                            bannerView
                                .padding(.horizontal, 16)
                                .padding(.bottom, 16)
                        } else {
                            EmptyView()
                        }
                    }
                    .background(Color(.systemBackground))
                ) {
                    // navigation destination for notification is defined in ServicesFlowView
                    ForEach(filteredNotifications) { notification in
                        NavigationLink(value: notification) {
                            NotificationRow(notification: notification, service: viewModel.service)
                        }
                        .buttonStyle(.plain)
                        .padding(.horizontal, 16)
                        .padding(.bottom, 10)
                    }
                }
            }
        }
        .refreshable { viewModel.refreshNotifications() }
    }

    var body: some View {
        ZStack {
            // Color added to fill the whole frame
            // Color.clear
            ThemeManager.primaryBackground(for: viewModel.service, colorScheme: colorScheme)
                .ignoresSafeArea(.all)
            
            VStack(alignment: .leading, spacing: 0) {
                if viewModel.notifications.isEmpty {
                    noDataView
                } else {
                    VStack(spacing: 0) {
                        if filteredNotifications.isEmpty {
                            noResultView
                        } else {
                            notificationsList
                        }
                        if !isSearching {
                            footRowView
                        }
                    }
                    .searchable(
                        text: $searchText,
                        isPresented: $isSearching,
                        placement: .navigationBarDrawer(displayMode: .automatic)
                    )
                }
            }
        }
        .navigationBarTitleDisplayMode(.large)
        .navigationTitle(viewModel.service.title)
        .navigationBarTitleTextColor(ThemeManager.primaryText(for: viewModel.service, colorScheme: colorScheme))
        .onAppear {
            viewModel.refreshNotifications()
        }
        .onChange(of: scenePhase) {
            if scenePhase == .active {
                viewModel.refreshNotifications()
                
                // Hide widget tutorial when coming back to app
                isWidgetTutorialVisible = false
            }
        }
    }
}

#if DEBUG
    #Preview("Default") {
        NavigationStack {
            NotificationsList(viewModel: ServiceScreenViewModel(service: .mock))
                .navigationTitle("preview")
        }
    }

    #Preview("Preview") {
        NavigationStack {
            NotificationsList(viewModel: ServiceScreenViewModel(service: .mockColored))
                .navigationTitle("preview")
        }
    }
#endif
