//
//  NotificationRow.swift
//  Appio
//
//  Created by gondo on 18/03/2025.
//

import SwiftUI

struct NotificationRow: View {
    let notification: NotificationEntity
    let service: ServiceEntity

    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(notification.payload.subtitle)
                        .lineLimit(1)
                        .font(.headline)
                        .foregroundStyle(ThemeManager.primaryText(for: service, colorScheme: colorScheme))

                    Spacer()

                    Text(notification.receivedAt.humanReadable())
                        .font(.caption)
                        .foregroundStyle(ThemeManager.secondaryText(for: service, colorScheme: colorScheme))
                        .multilineTextAlignment(.trailing)
                }

                Text(notification.payload.message)
                    .lineLimit(3)
                    .font(.subheadline)
                    .foregroundStyle(ThemeManager.primaryText(for: service, colorScheme: colorScheme))
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(UIColor.tertiarySystemGroupedBackground))
        .cornerRadius(UIConstants.smallCellCornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: UIConstants.bigCellCornerRadius)
                .strokeBorder(Color(UIColor.systemBackground).opacity(0.3))
        )
    }
}

#if DEBUG
    #Preview {
        NotificationRow(notification: NotificationEntity.mock, service: ServiceEntity.mock)
    }

    #Preview {
        NotificationRow(notification: NotificationEntity.mockB, service: ServiceEntity.mock)
    }

    #Preview("Colored") {
        NotificationRow(notification: NotificationEntity.mockC, service: ServiceEntity.mockColored)
    }
#endif
