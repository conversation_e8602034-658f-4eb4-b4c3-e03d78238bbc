//
//  NotificationDetails.swift
//  Appio
//
//  Created by gondo on 25/03/2025.
//

import SwiftUI

struct NotificationDetails: View {
    var notification: NotificationEntity
    let service: ServiceEntity?

    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        VStack(spacing: 0) {
            ScrollView {
                if let imageUrl = notification.payload.imageUrl, !imageUrl.isEmpty {
                    AsyncImageView(urlString: imageUrl)
                        .aspectRatio(16 / 9, contentMode: .fit)
                        .clipped()
                }

                Text(notification.receivedAt.humanReadable(separator: " - "))
                    .font(.footnote)
                    .foregroundStyle(ThemeManager.secondaryText(for: service, colorScheme: colorScheme))
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()

                Text(notification.payload.message)
                    .foregroundStyle(ThemeManager.primaryText(for: service, colorScheme: colorScheme))
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal)
            }
        }
        .safeAreaInset(edge: .bottom) {
            if let link = notification.payload.link,
               let url = URL(string: link),
               UIApplication.shared.canOpenURL(url)
            {
                ZStack {
                    ZStack(alignment: .top) {
                        BackgroundColor()
                        VStack(spacing: 0) {
                            Divider()
                            
                            AppioButton("Open Link") {
                                UIApplication.shared.open(url)
                            }
                            .tint(ThemeManager.accent(for: service, colorScheme: colorScheme))
                            .padding(UIConstants.largeSpacing)
                        }
                    }
                    .frame(height: 60)
                }
            }
        }
        .navigationTitle(notification.payload.subtitle)
    }
}

#if DEBUG
    #Preview {
        NotificationDetails(notification: .mock, service: ServiceEntity.mock)
    }

    #Preview("image") {
        NotificationDetails(notification: .mockB, service: ServiceEntity.mock)
    }

    #Preview("Colored") {
        NotificationDetails(notification: .mock, service: ServiceEntity.mockColored)
    }
#endif
