//
//  ThemeManager.swift
//  Appio
//
//  Created by gondo on 17/09/2025.
//

import SwiftUI

/// ThemeManager provides computed colors based on a ServiceEntity's theme configuration
enum ThemeManager {
    // MARK: - Default Colors (fallbacks when service colors are nil)

    private struct DefaultColors {
        static let primary: Color = .primary
        static let secondary: Color = .secondary
        static let accent: Color = .accentColor
        static let background: Color = Color(.systemGroupedBackground)
    }

    // MARK: - Computed Theme Colors

    /// Primary text color - uses service textColor if available, otherwise default
    static func primaryText(for service: ServiceEntity?, colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let textColorString = service.textColor,
              !textColorString.isEmpty else {
            return DefaultColors.primary
        }

        return textColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.primary)
    }

    /// Secondary text color - derived from primary text or uses default
    static func secondaryText(for service: ServiceEntity?, colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let textColorString = service.textColor,
              !textColorString.isEmpty else {
            return DefaultColors.secondary
        }

        // Use the service text color with reduced opacity for secondary text
        let serviceColor = textColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.primary)
        return serviceColor.opacity(0.7)
    }

    /// Primary background color - uses service backgroundColor if available, otherwise default
    static func primaryBackground(for service: ServiceEntity?, colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let backgroundColorString = service.backgroundColor,
              !backgroundColorString.isEmpty else {
            return DefaultColors.background
        }

        return backgroundColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.background)
    }

    /// Accent color - uses service textColor as accent, or default accent
    static func accent(for service: ServiceEntity?, colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let textColorString = service.textColor,
              !textColorString.isEmpty else {
            return DefaultColors.accent
        }

        return textColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.accent)
    }

}

// MARK: - View Extensions for Navigation Bar Theming

extension View {
    func navigationBarTitleTextColor(_ color: Color) -> some View {
        self.modifier(NavigationBarTitleColorModifier(color: color))
    }
}

struct NavigationBarTitleColorModifier: ViewModifier {
    let color: Color

    func body(content: Content) -> some View {
        content
            .onAppear {
                let appearance = UINavigationBarAppearance()
                appearance.configureWithDefaultBackground()

                let uiColor = UIColor(color)

                // Style navigation title
                appearance.largeTitleTextAttributes = [.foregroundColor: uiColor]
                appearance.titleTextAttributes = [.foregroundColor: uiColor]

                // Style back button
//                appearance.setBackIndicatorImage(UIImage(systemName: "chevron.left"), transitionMaskImage: UIImage(systemName: "chevron.left"))
                appearance.backButtonAppearance.normal.titleTextAttributes = [.foregroundColor: uiColor]
                appearance.buttonAppearance.normal.titleTextAttributes = [.foregroundColor: uiColor]

                // Apply tint color for back button icon
                UINavigationBar.appearance().tintColor = uiColor

                UINavigationBar.appearance().standardAppearance = appearance
                UINavigationBar.appearance().scrollEdgeAppearance = appearance
                UINavigationBar.appearance().compactAppearance = appearance
            }
    }
}
