//
//  ThemeManager.swift
//  Appio
//
//  Created by gondo on 17/09/2025.
//

import SwiftUI

/// ThemeManager provides computed colors based on a ServiceEntity's theme configuration
enum ThemeManager {
    // MARK: - Default Colors (fallbacks when service colors are nil)

    private struct DefaultColors {
        static let primary: Color = .primary
        static let secondary: Color = .secondary
        static let accent: Color = .accentColor
        static let background: Color = Color(.systemGroupedBackground)
    }

    // MARK: - Computed Theme Colors

    /// Primary text color - uses service textColor if available, otherwise default
    static func primaryText(for service: ServiceEntity?, colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let textColorString = service.textColor,
              !textColorString.isEmpty else {
            return DefaultColors.primary
        }

        return textColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.primary)
    }

    /// Secondary text color - derived from primary text or uses default
    static func secondaryText(for service: ServiceEntity?, colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let textColorString = service.textColor,
              !textColorString.isEmpty else {
            return DefaultColors.secondary
        }

        // Use the service text color with reduced opacity for secondary text
        let serviceColor = textColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.primary)
        return serviceColor.opacity(0.7)
    }

    /// Primary background color - uses service backgroundColor if available, otherwise default
    static func primaryBackground(for service: ServiceEntity?, colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let backgroundColorString = service.backgroundColor,
              !backgroundColorString.isEmpty else {
            return DefaultColors.background
        }

        return backgroundColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.background)
    }

    /// Accent color - uses service textColor as accent, or default accent
    static func accent(for service: ServiceEntity?, colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let textColorString = service.textColor,
              !textColorString.isEmpty else {
            return DefaultColors.accent
        }

        return textColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.accent)
    }

    /// Toolbar color scheme - determines if toolbar should use light or dark appearance based on service colors
    static func toolbarColorScheme(for service: ServiceEntity?, colorScheme: ColorScheme) -> ColorScheme? {
        guard let service = service,
              let textColorString = service.textColor,
              !textColorString.isEmpty else {
            return nil // Use system default
        }

        // If service has custom text color, determine appropriate toolbar color scheme
        let serviceTextColor = textColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.primary)

        // Convert to UIColor to get luminance
        let uiColor = UIColor(serviceTextColor)
        var red: CGFloat = 0, green: CGFloat = 0, blue: CGFloat = 0, alpha: CGFloat = 0
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

        // Calculate luminance (perceived brightness)
        let luminance = 0.299 * red + 0.587 * green + 0.114 * blue

        // If text is light, use dark toolbar; if text is dark, use light toolbar
        return luminance > 0.5 ? .light : .dark
    }
}
