//
//  ServiceManager.swift
//  Appio
//
//  Created by gondo on 04/03/2025.
//

import Foundation

enum ServiceManager {
    /// Also widgets
    static func fetchServiceWithWidgetsBy(id serviceId: String, customerUserId: String) async throws -> ServiceEntity {
        let response = try await APIService.shared.fetchService(id: serviceId)

        let service: ServiceEntity
        if let existing = list().first(where: { $0.id == response.id }) {
            service = ServiceEntity(from: response, customerUserId: customerUserId, existing: existing)
        } else {
            service = ServiceEntity(from: response, customerUserId: customerUserId)
        }

        // update widgets
        if let widgets = response.widgets {
            WidgetsManager.mergeNewWidgets(new: widgets, serviceId: service.id)
        }

        await preCacheServiceImages(for: response)
        StorageManager.appendService(service: service, overwrites: true)
        return service
    }

    /// merge new and existing services, also widgets
    static func refreshAllServiesAndWidgets(device: DeviceEntity) async {
        do {
            let response = try await APIService.shared.fetchAllServices(deviceId: device.id)
            mergeNewServicesAndWidgets(newServices: response, current: list())
            await withTaskGroup(of: Void.self) { group in
                for serviceResponse in response {
                    group.addTask {
                        await self.preCacheServiceImages(for: serviceResponse)
                    }
                }
            }
        } catch ApiError.debounce {
            // Silently ignore debounce errors
            return
        } catch {
            Log.shared.error("Refreshing services failed with error: \(error)")
        }
    }

    /// deletes services that are not set in API, merge local services, also widgets
    static func mergeNewServicesAndWidgets(newServices: [ServiceResponse], current: [ServiceEntity]) {
        var merged: [ServiceEntity] = []

        for newService in newServices {
            if let existing = current.first(where: { $0.id == newService.id }) {
                merged.append(ServiceEntity(from: newService, customerUserId: existing.customerUserId, existing: existing))
            } else {
                merged.append(ServiceEntity(from: newService, customerUserId: nil))
            }

            // update widgets
            if let widgets = newService.widgets {
                WidgetsManager.mergeNewWidgets(new: widgets, serviceId: newService.id)
            }
        }

        StorageManager.services = merged
    }

    static func list() -> [ServiceEntity] {
        return StorageManager.services
    }

    static func get(id: String) -> ServiceEntity? {
        return StorageManager.services.first(where: { $0.id == id })
    }

    /// overwrites local version
    static func updateService(service: ServiceEntity) {
        guard let index = StorageManager.services.firstIndex(where: { $0.id == service.id }) else {
            return
        }

        var updatedServices = StorageManager.services
        updatedServices[index] = service
        StorageManager.services = updatedServices // overwrite existing with update
    }

    /** // N2H: notificationsEnabled per service. once ServiceEntity setting page is created
        static func updateService(service: ServiceEntity) async throws -> Bool {
            updateService(service: service)

            let ok = try await APIService.shared.updateService(
                serviceId: serviceId,
                notificationsEnabled: service.notifiationsEnabled
            )
            if ok {
                var mutableService = service
                mutableService.markSynced()

                guard let index = StorageManager.services.firstIndex(where: { $0.id == service.id }) else {
                    return
                }
                var newUpdatedServices = StorageManager.services
                newUpdatedServices[index] = mutableService
                StorageManager.services = newUpdatedServices
            }

            return ok
        }

        static func sync() async {
            for service in StorageManager.services {
                if service.isOutOfSync() {
                    do {
                        let ok = try await updateService(service: service)
                        if !ok {
     Log.shared.error("Service update failed")
                        }
                    } catch {
     Log.shared.error("Service sync failed with error: \(error)")
                    }
                }
            }
        }
     **/

    private static func preCacheServiceImages(for service: ServiceResponse) async {
        await ImageManager.shared.preCacheImage(service.logoURL)
        await ImageManager.shared.preCacheImage(service.bannerURL)
    }
}
